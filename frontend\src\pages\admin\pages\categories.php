<!-- Categories Management -->
<div class="space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm p-6">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-800"><PERSON><PERSON><PERSON></h1>
                <p class="text-gray-600 mt-1">Kelola kategori berita untuk mengorganisir konten</p>
            </div>
            <div class="mt-4 md:mt-0">
                <button onclick="showAddCategoryModal()" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i>
                    Tambah Kategori
                </button>
            </div>
        </div>
    </div>
    
    <!-- Categories Grid -->
    <div id="categories-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Loading state -->
        <div class="col-span-full flex items-center justify-center py-12">
            <div class="flex items-center">
                <i class="fas fa-spinner fa-spin text-2xl text-gray-400 mr-3"></i>
                <span class="text-gray-500">Memuat kategori...</span>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Category Modal -->
<div id="category-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 id="modal-title" class="text-lg font-semibold text-gray-800">Tambah Kategori</h3>
            </div>
            <div class="p-6">
                <form id="category-form" class="space-y-4">
                    <input type="hidden" id="category-id" value="">
                    
                    <div>
                        <label for="category-name" class="block text-sm font-medium text-gray-700 mb-2">
                            Nama Kategori <span class="text-red-500">*</span>
                        </label>
                        <input type="text" id="category-name" name="name" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                               placeholder="Masukkan nama kategori...">
                    </div>
                    
                    <div>
                        <label for="category-color" class="block text-sm font-medium text-gray-700 mb-2">
                            Warna Kategori
                        </label>
                        <div class="flex items-center space-x-3">
                            <input type="color" id="category-color" name="color" value="#3B82F6"
                                   class="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer">
                            <input type="text" id="category-color-text" value="#3B82F6"
                                   class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="#3B82F6">
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" onclick="hideCategoryModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors duration-200">
                            Batal
                        </button>
                        <button type="submit" id="save-category-btn" class="px-6 py-2 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                            <i class="fas fa-save mr-2"></i>
                            Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
let categories = [];

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadCategories();
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    // Color picker sync
    document.getElementById('category-color').addEventListener('input', function() {
        document.getElementById('category-color-text').value = this.value;
    });
    
    document.getElementById('category-color-text').addEventListener('input', function() {
        const color = this.value;
        if (/^#[0-9A-F]{6}$/i.test(color)) {
            document.getElementById('category-color').value = color;
        }
    });
    
    // Form submission
    document.getElementById('category-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveCategory();
    });
    
    // Close modal on outside click
    document.getElementById('category-modal').addEventListener('click', function(e) {
        if (e.target === this) {
            hideCategoryModal();
        }
    });
}

// Load categories
async function loadCategories() {
    try {
        const data = await safeFetch(`${API_BASE}?action=get_categories`);
        
        if (data.success && data.data) {
            categories = data.data;
            renderCategories();
        } else {
            document.getElementById('categories-grid').innerHTML = 
                '<div class="col-span-full text-center py-12 text-gray-500">Tidak ada kategori</div>';
        }
    } catch (error) {
        console.error('Error loading categories:', error);
        document.getElementById('categories-grid').innerHTML = 
            '<div class="col-span-full text-center py-12 text-red-500">Error memuat kategori</div>';
    }
}

// Render categories
function renderCategories() {
    const grid = document.getElementById('categories-grid');
    
    if (categories.length === 0) {
        grid.innerHTML = '<div class="col-span-full text-center py-12 text-gray-500">Belum ada kategori</div>';
        return;
    }
    
    grid.innerHTML = categories.map(category => `
        <div class="bg-white rounded-xl shadow-sm p-6 card-hover border-l-4" style="border-left-color: ${category.color || '#3B82F6'}">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="w-4 h-4 rounded-full mr-3" style="background-color: ${category.color || '#3B82F6'}"></div>
                    <h3 class="text-lg font-semibold text-gray-800">${category.name}</h3>
                </div>
                <div class="flex items-center space-x-2">
                    <button onclick="editCategory(${category.id})" class="text-blue-600 hover:text-blue-800 p-1" title="Edit">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button onclick="deleteCategory(${category.id})" class="text-red-600 hover:text-red-800 p-1" title="Hapus">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            
            <div class="space-y-2 text-sm text-gray-600">
                <div class="flex items-center justify-between">
                    <span>Total Berita:</span>
                    <span class="font-medium" id="category-count-${category.id}">-</span>
                </div>
                <div class="flex items-center justify-between">
                    <span>Warna:</span>
                    <span class="font-mono text-xs">${category.color || '#3B82F6'}</span>
                </div>
            </div>
            
            <div class="mt-4 pt-4 border-t border-gray-200">
                <a href="?page=news&category=${category.id}" class="text-primary hover:text-blue-700 text-sm font-medium">
                    Lihat Berita <i class="fas fa-arrow-right ml-1"></i>
                </a>
            </div>
        </div>
    `).join('');
    
    // Load news count for each category
    loadCategoriesNewsCount();
}

// Load news count for categories
async function loadCategoriesNewsCount() {
    try {
        const data = await safeFetch(`${API_BASE}?action=get_news`);
        
        if (data.success && data.data) {
            const newsByCategory = {};
            data.data.forEach(news => {
                const categoryId = news.category_id || 1;
                newsByCategory[categoryId] = (newsByCategory[categoryId] || 0) + 1;
            });
            
            categories.forEach(category => {
                const countElement = document.getElementById(`category-count-${category.id}`);
                if (countElement) {
                    countElement.textContent = newsByCategory[category.id] || 0;
                }
            });
        }
    } catch (error) {
        console.error('Error loading news count:', error);
    }
}

// Modal functions
function showAddCategoryModal() {
    document.getElementById('modal-title').textContent = 'Tambah Kategori';
    document.getElementById('category-form').reset();
    document.getElementById('category-id').value = '';
    document.getElementById('category-color').value = '#3B82F6';
    document.getElementById('category-color-text').value = '#3B82F6';
    document.getElementById('category-modal').classList.remove('hidden');
}

function hideCategoryModal() {
    document.getElementById('category-modal').classList.add('hidden');
}

function editCategory(id) {
    const category = categories.find(c => c.id == id);
    if (!category) return;
    
    document.getElementById('modal-title').textContent = 'Edit Kategori';
    document.getElementById('category-id').value = category.id;
    document.getElementById('category-name').value = category.name;
    document.getElementById('category-color').value = category.color || '#3B82F6';
    document.getElementById('category-color-text').value = category.color || '#3B82F6';
    document.getElementById('category-modal').classList.remove('hidden');
}

// Save category
async function saveCategory() {
    const form = document.getElementById('category-form');
    const formData = new FormData();
    
    const id = document.getElementById('category-id').value;
    const name = document.getElementById('category-name').value.trim();
    const color = document.getElementById('category-color').value;
    
    if (!name) {
        showNotification('Nama kategori harus diisi', 'error');
        return;
    }
    
    // Check for duplicate names (excluding current category when editing)
    const existingCategory = categories.find(c => c.name.toLowerCase() === name.toLowerCase() && c.id != id);
    if (existingCategory) {
        showNotification('Nama kategori sudah ada', 'error');
        return;
    }
    
    const action = id ? 'update_category' : 'add_category';
    formData.append('action', action);
    if (id) formData.append('id', id);
    formData.append('name', name);
    formData.append('color', color);
    
    // Show loading state
    const saveBtn = document.getElementById('save-category-btn');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Menyimpan...';
    saveBtn.disabled = true;
    
    try {
        const result = await safeFetch(API_BASE, {
            method: 'POST',
            body: formData
        });
        
        if (result.success) {
            showNotification(id ? 'Kategori berhasil diupdate' : 'Kategori berhasil ditambahkan', 'success');
            hideCategoryModal();
            loadCategories();
        } else {
            showNotification(result.message || 'Gagal menyimpan kategori', 'error');
        }
    } catch (error) {
        console.error('Error saving category:', error);
        showNotification('Error menyimpan kategori', 'error');
    } finally {
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    }
}

// Delete category
async function deleteCategory(id) {
    const category = categories.find(c => c.id == id);
    if (!category) return;
    
    // Check if category has news
    const countElement = document.getElementById(`category-count-${id}`);
    const newsCount = parseInt(countElement?.textContent || '0');
    
    if (newsCount > 0) {
        if (!confirm(`Kategori "${category.name}" memiliki ${newsCount} berita. Berita akan dipindahkan ke kategori "Umum". Lanjutkan?`)) {
            return;
        }
    } else {
        if (!confirm(`Apakah Anda yakin ingin menghapus kategori "${category.name}"?`)) {
            return;
        }
    }
    
    try {
        const formData = new FormData();
        formData.append('action', 'delete_category');
        formData.append('id', id);
        
        const result = await safeFetch(API_BASE, {
            method: 'POST',
            body: formData
        });
        
        if (result.success) {
            showNotification('Kategori berhasil dihapus', 'success');
            loadCategories();
        } else {
            showNotification(result.message || 'Gagal menghapus kategori', 'error');
        }
    } catch (error) {
        console.error('Error deleting category:', error);
        showNotification('Error menghapus kategori', 'error');
    }
}
</script>
