<!-- Settings -->
<div class="max-w-4xl mx-auto space-y-6">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-sm p-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-800">Pengaturan Website</h1>
            <p class="text-gray-600 mt-1">Konfigurasi pengaturan umum website Anda</p>
        </div>
    </div>

    <form id="settings-form" class="space-y-6">
        <!-- Website Information -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Informasi Website</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div class="lg:col-span-2">
                    <label for="website_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Nama Website <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="website_name" name="website_name" required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="Masukkan nama website...">
                </div>
                <div class="lg:col-span-2">
                    <label for="website_description" class="block text-sm font-medium text-gray-700 mb-2">
                        Deskripsi Website
                    </label>
                    <textarea id="website_description" name="website_description" rows="3"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Deskripsi singkat tentang website..."></textarea>
                </div>
            </div>
        </div>

        <!-- Logo Upload -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Logo Website</h3>
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                    <label for="logo" class="block text-sm font-medium text-gray-700 mb-2">Upload Logo</label>
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-primary transition-colors duration-200">
                        <input type="file" id="logo" name="logo" accept="image/*" class="hidden" onchange="previewLogo(this)">
                        <div id="logo-upload-area" onclick="document.getElementById('logo').click()" class="cursor-pointer">
                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-2"></i>
                            <p class="text-gray-600">Klik untuk upload logo</p>
                            <p class="text-sm text-gray-500 mt-1">Format: PNG, JPG. Max: 2MB</p>
                        </div>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Logo Saat Ini</label>
                    <div id="current-logo" class="w-full h-32 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                        <i class="fas fa-image text-4xl text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Color Scheme -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Skema Warna</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="primary_color" class="block text-sm font-medium text-gray-700 mb-2">Warna Utama</label>
                    <div class="flex items-center space-x-3">
                        <input type="color" id="primary_color" name="primary_color" value="#3B82F6"
                               class="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer">
                        <input type="text" id="primary_color_text" value="#3B82F6"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>
                <div>
                    <label for="secondary_color" class="block text-sm font-medium text-gray-700 mb-2">Warna Sekunder</label>
                    <div class="flex items-center space-x-3">
                        <input type="color" id="secondary_color" name="secondary_color" value="#10B981"
                               class="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer">
                        <input type="text" id="secondary_color_text" value="#10B981"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>
                <div>
                    <label for="accent_color" class="block text-sm font-medium text-gray-700 mb-2">Warna Aksen</label>
                    <div class="flex items-center space-x-3">
                        <input type="color" id="accent_color" name="accent_color" value="#F59E0B"
                               class="w-12 h-10 border border-gray-300 rounded-lg cursor-pointer">
                        <input type="text" id="accent_color_text" value="#F59E0B"
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                </div>
            </div>
            <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                <p class="text-sm text-gray-600 mb-2">Preview Warna:</p>
                <div class="flex space-x-2">
                    <div id="color-preview-primary" class="w-8 h-8 rounded" style="background-color: #3B82F6"></div>
                    <div id="color-preview-secondary" class="w-8 h-8 rounded" style="background-color: #10B981"></div>
                    <div id="color-preview-accent" class="w-8 h-8 rounded" style="background-color: #F59E0B"></div>
                </div>
            </div>
        </div>

        <!-- SEO Settings -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Pengaturan SEO</h3>
            <div class="space-y-4">
                <div>
                    <label for="meta_keywords" class="block text-sm font-medium text-gray-700 mb-2">Meta Keywords</label>
                    <input type="text" id="meta_keywords" name="meta_keywords"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="berita, news, portal berita, indonesia">
                </div>
                <div>
                    <label for="meta_description" class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                    <textarea id="meta_description" name="meta_description" rows="2"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                              placeholder="Deskripsi website untuk mesin pencari..."></textarea>
                </div>
            </div>
        </div>

        <!-- Social Media -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-6">Media Sosial</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="facebook_url" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fab fa-facebook text-blue-600 mr-2"></i>Facebook
                    </label>
                    <input type="url" id="facebook_url" name="facebook_url"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="https://facebook.com/username">
                </div>
                <div>
                    <label for="twitter_url" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fab fa-twitter text-blue-400 mr-2"></i>Twitter
                    </label>
                    <input type="url" id="twitter_url" name="twitter_url"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="https://twitter.com/username">
                </div>
                <div>
                    <label for="instagram_url" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fab fa-instagram text-pink-600 mr-2"></i>Instagram
                    </label>
                    <input type="url" id="instagram_url" name="instagram_url"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="https://instagram.com/username">
                </div>
                <div>
                    <label for="youtube_url" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fab fa-youtube text-red-600 mr-2"></i>YouTube
                    </label>
                    <input type="url" id="youtube_url" name="youtube_url"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                           placeholder="https://youtube.com/channel/...">
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <div class="flex flex-col sm:flex-row gap-3 justify-end">
                <button type="button" onclick="resetSettings()" class="px-6 py-3 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors duration-200">
                    <i class="fas fa-undo mr-2"></i>
                    Reset
                </button>
                <button type="submit" id="save-settings-btn" class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    Simpan Pengaturan
                </button>
            </div>
        </div>
    </form>
</div>

<script>
// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadSettings();
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    // Color picker sync
    const colorInputs = ['primary_color', 'secondary_color', 'accent_color'];
    
    colorInputs.forEach(colorName => {
        const colorPicker = document.getElementById(colorName);
        const colorText = document.getElementById(colorName + '_text');
        const colorPreview = document.getElementById('color-preview-' + colorName.replace('_color', ''));
        
        colorPicker.addEventListener('input', function() {
            colorText.value = this.value;
            colorPreview.style.backgroundColor = this.value;
        });
        
        colorText.addEventListener('input', function() {
            const color = this.value;
            if (/^#[0-9A-F]{6}$/i.test(color)) {
                colorPicker.value = color;
                colorPreview.style.backgroundColor = color;
            }
        });
    });
    
    // Form submission
    document.getElementById('settings-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveSettings();
    });
}

// Load current settings
async function loadSettings() {
    try {
        const data = await safeFetch(`${API_BASE}?action=get_settings`);
        
        if (data.success && data.data) {
            const settings = data.data;
            
            // Fill form fields
            document.getElementById('website_name').value = settings.website_name || '';
            document.getElementById('website_description').value = settings.website_description || '';
            document.getElementById('primary_color').value = settings.primary_color || '#3B82F6';
            document.getElementById('primary_color_text').value = settings.primary_color || '#3B82F6';
            document.getElementById('secondary_color').value = settings.secondary_color || '#10B981';
            document.getElementById('secondary_color_text').value = settings.secondary_color || '#10B981';
            document.getElementById('accent_color').value = settings.accent_color || '#F59E0B';
            document.getElementById('accent_color_text').value = settings.accent_color || '#F59E0B';
            document.getElementById('meta_keywords').value = settings.meta_keywords || '';
            document.getElementById('meta_description').value = settings.meta_description || '';
            document.getElementById('facebook_url').value = settings.facebook_url || '';
            document.getElementById('twitter_url').value = settings.twitter_url || '';
            document.getElementById('instagram_url').value = settings.instagram_url || '';
            document.getElementById('youtube_url').value = settings.youtube_url || '';
            
            // Update color previews
            document.getElementById('color-preview-primary').style.backgroundColor = settings.primary_color || '#3B82F6';
            document.getElementById('color-preview-secondary').style.backgroundColor = settings.secondary_color || '#10B981';
            document.getElementById('color-preview-accent').style.backgroundColor = settings.accent_color || '#F59E0B';
            
            // Show current logo
            if (settings.website_logo) {
                document.getElementById('current-logo').innerHTML = 
                    `<img src="${settings.website_logo}" alt="Current Logo" class="w-full h-full object-contain">`;
            }
        }
    } catch (error) {
        console.error('Error loading settings:', error);
        showNotification('Error memuat pengaturan', 'error');
    }
}

// Preview logo
function previewLogo(input) {
    const preview = document.getElementById('current-logo');
    
    if (input.files && input.files[0]) {
        const file = input.files[0];
        
        // Validate file size (2MB)
        if (file.size > 2 * 1024 * 1024) {
            showNotification('Ukuran file terlalu besar. Maksimal 2MB', 'error');
            input.value = '';
            return;
        }
        
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/png'];
        if (!allowedTypes.includes(file.type)) {
            showNotification('Format file tidak didukung. Gunakan PNG atau JPG', 'error');
            input.value = '';
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="w-full h-full object-contain">`;
        };
        reader.readAsDataURL(file);
    }
}

// Save settings
async function saveSettings() {
    const form = document.getElementById('settings-form');
    const formData = new FormData();
    
    // Get form values
    const websiteName = document.getElementById('website_name').value.trim();
    
    if (!websiteName) {
        showNotification('Nama website harus diisi', 'error');
        document.getElementById('website_name').focus();
        return;
    }
    
    // Prepare form data
    formData.append('action', 'update_settings');
    formData.append('website_name', websiteName);
    formData.append('website_description', document.getElementById('website_description').value.trim());
    formData.append('primary_color', document.getElementById('primary_color').value);
    formData.append('secondary_color', document.getElementById('secondary_color').value);
    formData.append('accent_color', document.getElementById('accent_color').value);
    formData.append('meta_keywords', document.getElementById('meta_keywords').value.trim());
    formData.append('meta_description', document.getElementById('meta_description').value.trim());
    formData.append('facebook_url', document.getElementById('facebook_url').value.trim());
    formData.append('twitter_url', document.getElementById('twitter_url').value.trim());
    formData.append('instagram_url', document.getElementById('instagram_url').value.trim());
    formData.append('youtube_url', document.getElementById('youtube_url').value.trim());
    
    // Add logo if selected
    const logoFile = document.getElementById('logo').files[0];
    if (logoFile) {
        formData.append('logo', logoFile);
    }
    
    // Show loading state
    const saveBtn = document.getElementById('save-settings-btn');
    const originalText = saveBtn.innerHTML;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Menyimpan...';
    saveBtn.disabled = true;
    
    try {
        const result = await safeFetch(API_BASE, {
            method: 'POST',
            body: formData
        });
        
        if (result.success) {
            showNotification('Pengaturan berhasil disimpan', 'success');
            setTimeout(() => {
                location.reload(); // Reload to apply new settings
            }, 1500);
        } else {
            showNotification(result.message || 'Gagal menyimpan pengaturan', 'error');
        }
    } catch (error) {
        console.error('Error saving settings:', error);
        showNotification('Error menyimpan pengaturan', 'error');
    } finally {
        saveBtn.innerHTML = originalText;
        saveBtn.disabled = false;
    }
}

// Reset settings
function resetSettings() {
    if (confirm('Apakah Anda yakin ingin mereset semua pengaturan ke default?')) {
        loadSettings();
        showNotification('Pengaturan direset ke default', 'info');
    }
}
</script>
