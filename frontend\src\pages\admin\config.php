<?php
// Database configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'react_news');

// Website settings
$website_settings = [
    'name' => 'React News Portal',
    'logo' => 'assets/logo.png',
    'primary_color' => '#3B82F6',
    'secondary_color' => '#1E40AF',
    'accent_color' => '#F59E0B'
];

// Database connection
function getConnection() {
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
        return $pdo;
    } catch(PDOException $e) {
        die("Connection failed: " . $e->getMessage());
    }
}

// Create tables if they don't exist
function createTables() {
    $pdo = getConnection();

    // Categories table first (referenced by posts)
    $sql = "CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        color VARCHAR(7) DEFAULT '#3B82F6',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);

    // Insert default categories if not exist
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM categories");
    $stmt->execute();
    $categoryCount = $stmt->fetchColumn();

    if ($categoryCount == 0) {
        $defaultCategories = [
            ['Umum', '#3B82F6'],
            ['Politik', '#EF4444'],
            ['Ekonomi', '#10B981'],
            ['Olahraga', '#F59E0B'],
            ['Teknologi', '#8B5CF6'],
            ['Hiburan', '#EC4899'],
            ['Kesehatan', '#06B6D4']
        ];

        $stmt = $pdo->prepare("INSERT INTO categories (name, color) VALUES (?, ?)");
        foreach ($defaultCategories as $category) {
            $stmt->execute($category);
        }
    }

    // Posts table (matching backend structure)
    $sql = "CREATE TABLE IF NOT EXISTS posts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        image VARCHAR(255) NULL,
        category_id INT DEFAULT 1,
        views INT DEFAULT 0,
        status ENUM('draft', 'published') DEFAULT 'draft',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (category_id) REFERENCES categories(id)
    )";
    $pdo->exec($sql);

    // Admin table
    $sql = "CREATE TABLE IF NOT EXISTS admin (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);

    // Settings table
    $sql = "CREATE TABLE IF NOT EXISTS settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT NOT NULL,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )";
    $pdo->exec($sql);

    // Saved posts table
    $sql = "CREATE TABLE IF NOT EXISTS saved (
        id INT AUTO_INCREMENT PRIMARY KEY,
        post_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (post_id) REFERENCES posts(id) ON DELETE CASCADE
    )";
    $pdo->exec($sql);

    // Insert default settings if not exist
    $settings = [
        ['website_name', 'React News Portal'],
        ['website_logo', 'assets/logo.png'],
        ['primary_color', '#3B82F6'],
        ['secondary_color', '#1E40AF'],
        ['accent_color', '#F59E0B']
    ];

    $stmt = $pdo->prepare("INSERT IGNORE INTO settings (setting_key, setting_value) VALUES (?, ?)");
    foreach ($settings as $setting) {
        $stmt->execute($setting);
    }

    // Insert default admin if not exist
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM admin");
    $stmt->execute();
    $adminCount = $stmt->fetchColumn();

    if ($adminCount == 0) {
        $defaultPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO admin (username, password) VALUES (?, ?)");
        $stmt->execute(['admin', $defaultPassword]);
    }
}

// Initialize database
createTables();

// Helper functions for settings
function getSetting($key) {
    try {
        $pdo = getConnection();

        $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();

        if ($result) {
            return $result['setting_value'];
        }

        // Return defaults for common settings if not found
        $defaults = [
            'website_name' => 'React News Portal',
            'website_logo' => '/logo192.png',
            'website_description' => 'Portal berita terkini dan terpercaya',
            'primary_color' => '#3B82F6',
            'secondary_color' => '#10B981',
            'accent_color' => '#F59E0B'
        ];

        return $defaults[$key] ?? null;
    } catch (PDOException $e) {
        error_log("Error getting setting $key: " . $e->getMessage());

        // Return defaults if database error
        $defaults = [
            'website_name' => 'React News Portal',
            'website_logo' => '/logo192.png',
            'website_description' => 'Portal berita terkini dan terpercaya',
            'primary_color' => '#3B82F6',
            'secondary_color' => '#10B981',
            'accent_color' => '#F59E0B'
        ];

        return $defaults[$key] ?? null;
    }
}

function updateSetting($key, $value) {
    try {
        $pdo = getConnection();

        // Use simple key-value structure
        $stmt = $pdo->prepare("INSERT INTO settings (setting_key, setting_value)
                              VALUES (?, ?)
                              ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = NOW()");

        $result = $stmt->execute([$key, $value, $value]);

        if (!$result) {
            error_log("Failed to update setting $key: " . implode(', ', $stmt->errorInfo()));
            return false;
        }

        return true;
    } catch (PDOException $e) {
        error_log("Error updating setting $key: " . $e->getMessage());
        return false;
    }
}

// Helper function to generate slug
function generateSlug($text) {
    // Handle empty or null text
    if (empty($text)) {
        $text = 'untitled-post';
    }

    // Replace non-alphanumeric characters with hyphens
    $slug = preg_replace('/[^a-zA-Z0-9\s]/', '', $text);
    $slug = preg_replace('/\s+/', '-', trim($slug));
    $slug = strtolower($slug);

    // If slug is empty after processing, use default
    if (empty($slug)) {
        $slug = 'untitled-post';
    }

    // Ensure uniqueness
    $pdo = getConnection();
    $originalSlug = $slug;
    $counter = 1;

    while (true) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM posts WHERE slug = ?");
        $stmt->execute([$slug]);
        if ($stmt->fetchColumn() == 0) {
            break;
        }
        $slug = $originalSlug . '-' . $counter;
        $counter++;
    }

    return $slug;
}

// Function to get dashboard statistics
function getStats() {
    try {
        $pdo = getConnection();

        // Get total admin users
        $totalUsers = $pdo->query("SELECT COUNT(*) FROM admin")->fetchColumn();

        // Get total posts/news
        $totalNews = $pdo->query("SELECT COUNT(*) FROM posts")->fetchColumn();

        // Get published news
        $publishedNews = $pdo->query("SELECT COUNT(*) FROM posts WHERE status = 'published'")->fetchColumn();

        // Get draft news
        $draftNews = $pdo->query("SELECT COUNT(*) FROM posts WHERE status = 'draft'")->fetchColumn();

        // Get total categories
        $totalCategories = $pdo->query("SELECT COUNT(*) FROM categories")->fetchColumn();

        // Get total views (page views)
        $pageViews = $pdo->query("SELECT SUM(views) FROM posts")->fetchColumn() ?: 0;

        // Get total shares (if column exists)
        try {
            $totalShares = $pdo->query("SELECT SUM(share) FROM posts")->fetchColumn() ?: 0;
        } catch (Exception $e) {
            $totalShares = 0;
        }

        // Get total likes (if column exists)
        try {
            $totalLikes = $pdo->query("SELECT SUM(likes) FROM posts")->fetchColumn() ?: 0;
        } catch (Exception $e) {
            $totalLikes = 0;
        }

        // Get total saved posts
        $savedPosts = $pdo->query("SELECT COUNT(*) FROM saved")->fetchColumn();

        return [
            'total_users' => (int)$totalUsers,
            'total_news' => (int)$totalNews,
            'published_news' => (int)$publishedNews,
            'draft_news' => (int)$draftNews,
            'total_categories' => (int)$totalCategories,
            'page_views' => (int)$pageViews,
            'total_shares' => (int)$totalShares,
            'total_likes' => (int)$totalLikes,
            'saved_posts' => (int)$savedPosts,
            'revenue' => (int)($totalShares * 100) // Placeholder calculation
        ];
    } catch (Exception $e) {
        error_log("Error in getStats: " . $e->getMessage());
        return [
            'total_users' => 0,
            'total_news' => 0,
            'published_news' => 0,
            'draft_news' => 0,
            'total_categories' => 0,
            'page_views' => 0,
            'total_shares' => 0,
            'total_likes' => 0,
            'saved_posts' => 0,
            'revenue' => 0
        ];
    }
}

// Helper functions for categories
function getCategories() {
    try {
        $pdo = getConnection();
        $stmt = $pdo->query("SELECT * FROM categories ORDER BY name ASC");
        return $stmt->fetchAll();
    } catch (Exception $e) {
        error_log("Error in getCategories: " . $e->getMessage());
        return [];
    }
}

// Function to get recent activities
function getRecentActivities($limit = 5) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("
            SELECT p.id, p.title, p.created_at, 'post' as type
            FROM posts p
            ORDER BY p.created_at DESC
            LIMIT ?
        ");
        $stmt->execute([(int)$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error in getRecentActivities: " . $e->getMessage());
        return [];
    }
}

// Function to get popular posts
function getPopularPosts($limit = 5) {
    try {
        $pdo = getConnection();
        $stmt = $pdo->prepare("
            SELECT p.*, c.name as category_name
            FROM posts p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.status = 'published'
            ORDER BY p.views DESC
            LIMIT ?
        ");
        $stmt->execute([(int)$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        error_log("Error in getPopularPosts: " . $e->getMessage());
        return [];
    }
}

?>
